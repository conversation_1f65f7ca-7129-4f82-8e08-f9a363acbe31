/**
 * Shared event type definitions for analytics
 */

// -------------------- Auth & User Actions --------------------
export enum AuthEvent {
  SIGN_UP = 'User Signed Up',
  LOGIN = 'User Logged In',
  VERIFY_EMAIL = 'User Verify Email',
}

// -------------------- Suggested Actions & Guides --------------------
export enum GuideEvent {
  SUGGESTED_ACTION = 'User on Suggested Action',
  VIDEO_TUTORIAL_CLICK = 'video_tutorial_click',
  GUIDES_PAGE_CLICK = 'guides_page_click',
}

// -------------------- Amplify Features --------------------
export enum AmplifyEvent {
  AMPLIFY_BUTTON_CLICK = 'User Clicked Amplify Button',
  PROMPT_ARCHITECT_FINISH = 'User Finished Prompt Architect',
  REGENERATE_BUTTON_CLICK = 'User clicked Regenerate button in Amplify modal',
  SAVE_BUTTON_CLICK = 'User clicked Save button in Amplify modal',
  USE_BUTTON_CLICK = 'User clicked Use button in Amplify modal'
}

// --------------------  Playbook Features --------------------
export enum PlayBookEvent {
    CREATE_PROMPT = 'User Create a Prompt',
    DELETE_PROMPT = 'User Delete Prompt',
    PLAYBOOK_FEATURE = 'Playbook Feature',
    FOLDER_BUTTON_CLICK = 'clicked new folder button'
}

export enum PlayBookFlow {
  FOLDER_CREATION = 'folder creation',
  PROMPT_CREATION ='prompt creation',
  PROMPT_ARCHITECT = 'Prompt Architect',
}

export enum PlayBookStepName {
  OPENED = 'opened playbook drawer',
  CREATE_FOLDER = 'clicked create folder button',
  NEW_FOLDER = 'clicked new folder button',
  CREATE_PROMPT = 'clicked create prompt button',
  USE_PROMPT = 'used prompt from playbook',
  CLICK_NEXT_BUTTON = 'clicked next on stage:',
  NEW_PROMPT_BUTTON_CLICK = 'clicked new prompt button',
}

export enum PlayBookStepNumber {
  OPENED = '1',
  NEW_FOLDER = '2',
  CREATE_FOLDER = '3',
  USE_PROMPT = '2',
  CREATE_PROMPT = '3',
  PROMPT_CREATION = '2',
  NEW_PROMPT_BUTTON_CLICK = '2'
}

// -------------------- Chat Features --------------------
export enum ChatEvent {
  RESEARCH_MODE_CLICK = 'User Clicked Research Mode',
  ENHANCED_DOC_READER_CLICK = 'User Clicked Enhanced Doc Reader',
  MODEL_CHANGE = 'User Changed AI Model',
  CHAT_SHARE = 'User Shared Chat',
  TRANSFER_CHAT = 'User Transferred Chat',
  PIN_CHAT = 'User Pin Chat',
  UNPIN_CHAT = 'User Unpin Chat',
  DELETE_CHAT = 'User Delete a Chat',
  SAVE_TAG = 'User Create a Tag',
  CITE_CHECK = 'User Clicked Cite Check',
  VIEW_THINKING = 'User Clicked View thinking',
  EXPORT_CHAT_AS_PDF = 'User Export chat as PDF ',
  EXPORT_CHAT_AS_DOCX = 'User Export chat as DOCX ',
  UPDATE_CHAT_TITLE = "User update chat title",
  FEATURE_FEEDBACK_SUBMIT = 'User submit feature feedback',
  EXPORT_MESSAGE_AS_PDF = 'User Export message as PDF ',
  EXPORT_MESSAGE_AS_DOCX = 'User Export message as DOCX ',
  EDIT_MESSSAGE = 'User edit message',
  USER_FIRST_MESSAGE = 'User First Message',
  COPY_MESSAGE = 'User Copy Message'
}

// -------------------- Preferences & Theme --------------------
export enum PreferencesEvent {
  PROFESSIONAL_PREFERENCES = 'Professional Preferences Feature',
  TOGGLE_THEME = 'Toggle Theme',
}

// -------------------- Notifications Feature --------------------
export enum NotificationEvent {
  NOTIFICATION_BELL_CLICK = 'Notification Bell Icon Clicked',
  NOTIFICATION_LINK_CLICK = 'Notification Link Clicked',
  NOTIFICATION_READ_MORE_CLICK = 'Read More inside Notification body clicked',
}

export enum ServerErrorEvent {
  FILE_SIZE_EXCEEDED = "File Size Exceeded",
  FILE_UPLOAD_FAILED = "File Upload failed",
  FILE_DELETE_FAILED = "File Delete Failed",
  SERVER_CRASH = "Server Crash",
  CHAT_ROUTE_ERROR = "Chat Route Error",
  CHAT_DELETE_ERROR = "Chat Delete Error",
  GEMINI_PALM_API = "Error With Gemini Palm API",
  GEMINI_VERTEX_API = "Error With Gemini Vertex API",
  GPT_API_ERROR = "Error With GPT API",
  LLM_STREAM_ERROR = "LLM Stream Error",
  TIMEOUT_ERROR = "Request Timeout",
}

export enum ServerSuccessEvent {
  FILE_UPLOAD_SUCCESS = 'File Upload Succeeded',
  CHAT_REQUEST_RECEIVED = 'Chat Route Request Received',
  CHAT_REQUEST_SERVED = 'Chat Route Request Served',
  DOCUMENT_PROCESSING_COMPLETED = 'Chat Route - Doc Processing Completed',
  RAG_PROCESSING_COMPLETED = 'Chat Route - RAG Processing Completed',
  // INTERNET_SEARCH_COMPLETED = 'Chat Route - Internet Search Completed',
  PROMPT_PREPARATION_COMPLETED = 'Chat Route - Prompt Preparation Completed',
  FIRST_CHUNK_SENT = 'Chat Route - First Chunk Sent'
}

export enum PerformanceEvent {
  CHAT_ROUTE = 'Chat Resp Time',
  GET_INFORMATION_FUNC = 'RAG Fn Time',
  CREATE_DOC_FUNC = 'Create Doc Fn Time',
  UPDATE_DOC_FUNC = 'Update Doc Fn Time',
  REQUEST_SUGGESTION_FUNC = 'Req Suggestion Fn Time',
  GENERAL_PERFORMANCE_FUNC = 'General Fn Perf Time'
}

export enum SubscriptionEvent {
  SUBSCRIPTION_CREATED = 'User Subscribed to Premium Plan',
  SUBSCRIPTION_CANCELLED = 'User Cancelled Subscription',
  SUBSCRIPTION_DELETED = 'Subscription Deleted',
  REFERRAL = 'referral',
  CLICK_START_REFERRING_BUTTON = 'clicked start referring button'
}

export enum GoogleEvent {
  GOOGLE_BUTTON_CLICK = 'User Clicked Google Login Button',
}
// Type for all event types combined
export type EventType =
  | AuthEvent
  | GuideEvent
  | AmplifyEvent
  | ChatEvent
  | PreferencesEvent
  | NotificationEvent
  | PlayBookEvent
  | PlayBookStepName
  | PlayBookFlow
  | PlayBookStepNumber
  | ServerErrorEvent
  | ServerSuccessEvent
  | SubscriptionEvent
  | PerformanceEvent
  | GoogleEvent;
