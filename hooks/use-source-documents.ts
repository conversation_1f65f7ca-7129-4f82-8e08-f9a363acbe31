import useSWR from 'swr';
import { fetcher } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { Logger } from '@/lib/utils/Logger';

export function useSourceDocuments(chatId: string | null) {
  const { data, error, mutate } = useSWR(
    chatId ? `/api/get-chat-documents?chatId=${chatId}` : null,
    fetcher,
    {
      // Refresh data more frequently to ensure we have the latest documents
      refreshInterval: 5000, // Refresh every 5 seconds
      revalidateOnFocus: true,
      dedupingInterval: 2000 // Deduplicate requests within 2 seconds
    }
  );

  // State to hold combined documents from API and localStorage
  const [combinedDocuments, setCombinedDocuments] = useState<any[]>([]);

  useEffect(() => {
    if (!chatId) {
      setCombinedDocuments([]);
      return;
    }

    // Start with API data
    const apiDocuments = data || [];

    if (apiDocuments.length > 0) {
      Logger.info("Retrieved documents from API:", {
        count: apiDocuments.length,
        chatId
      });
      setCombinedDocuments(apiDocuments);
        return;
    }
  }, [chatId, data]);

  // Function to manually refresh the documents
  const refreshDocuments = async () => {
    // Logger.info("Manually refreshing documents for chat:", chatId);

    // First, refresh the API data
    const result = await mutate();
    return result;
  };

  return {
    documents: combinedDocuments,
    isLoading: !error && !data,
    isError: error,
    refresh: refreshDocuments
  };
}