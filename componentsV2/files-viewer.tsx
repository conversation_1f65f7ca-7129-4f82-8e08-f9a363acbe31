"use client";

import { X as CloseIcon, RefreshCw } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
	Di<PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogClose,
} from "@/components/ui/dialog";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { useFileViewer } from "@/hooks/use-file-viewer";
import { Logger } from "@/lib/utils/Logger";
import { FileIcon } from "./icons";
import { Message } from "ai";

interface FilesViewerProps {
	messages: Array<Message>; // Assuming messages is an array of message objects
	chatId: string;
	isOpen: boolean;
	onClose: () => void;
}

export function truncate(str: string, length: number): string {
	if (str.length <= length) return str;
	return str.slice(0, length) + "...";
}

export function FilesViewer({
	messages,
	chatId,
	isOpen: propIsOpen,
	onClose,
}: FilesViewerProps) {

	console.log("Messages in FilesViewer:", messages);
	// Add file viewer hook
	const { viewFile, isLoading: isViewLoading } = useFileViewer();

	// Use internal state to track open state, initialized from props
	const [isOpen, setIsOpen] = useState(propIsOpen);
	
	// Extract documents from messages instead of using useSourceDocuments
	const documents = messages.flatMap((message) => {
		if (!message.experimental_attachments) return [];
		
		return message.experimental_attachments.map((attachment) => ({
			id: attachment.document_id,
			filename: attachment.name,
			url: attachment.url,
			createdAt: message.createdAt || new Date().toISOString(),
			messageId: message.id,
			chatId: chatId
		}));
	});
	
	const [isRefreshing, setIsRefreshing] = useState(false);

	// Update internal state when prop changes
	useEffect(() => {
		setIsOpen(propIsOpen);
	}, [propIsOpen]);

	// Add this effect to handle playbook button visibility
	useEffect(() => {
		const playbookButton = document.querySelector(
			".top-\\[15px\\].right-\\[30px\\]",
		);
		if (playbookButton) {
			if (isOpen) {
				playbookButton.classList.add("opacity-0", "pointer-events-none");
			} else {
				playbookButton.classList.remove("opacity-0", "pointer-events-none");
			}
		}
	}, [isOpen]);

	const handleRefresh = () => {
		setIsRefreshing(true);
		// Since we're getting documents from messages, no need to refresh from API
		setTimeout(() => setIsRefreshing(false), 500);
	};

	const formatDate = (date: string | Date) => {
		try {
			return (
				new Date(date).toLocaleDateString() +
				" " +
				new Date(date).toLocaleTimeString([], {
					hour: "numeric",
					minute: "2-digit",
					hour12: true,
				})
			);
		} catch (error) {
			Logger.warn("Error formatting date:", { date, error });
			return "Unknown date";
		}
	};

	// Group documents by message ID
	const groupedDocuments = (documents || []).reduce(
		(groups: Record<string, any[]>, doc: any) => {
			const messageId = doc.messageId || "ungrouped";
			if (!groups[messageId]) {
				groups[messageId] = [];
			}
			groups[messageId].push(doc);
			return groups;
		},
		{},
	);

	// Sort message groups by the oldest document in each group
	const sortedGroups = Object.entries(groupedDocuments).sort((a, b) => {
		const aDate = new Date(a[1][0].createdAt).getTime();
		const bDate = new Date(b[1][0].createdAt).getTime();
		return aDate - bDate;
	});

	// Handle closing the drawer
	const handleOpenChange = (open: boolean) => {
		setIsOpen(open);
		if (!open) {
			onClose();
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleOpenChange}>
			<DialogContent className="sm:max-w-[600px] max-w-[90vw] max-h-[80vh] flex flex-col overflow-hidden">
				<DialogHeader className="flex flex-row justify-between items-center">
					<DialogTitle className="text-lg font-semibold">
						Files ({documents?.length || 0})
					</DialogTitle>
					<div className="flex items-center gap-2">
						<Button
							variant="ghost"
							size="icon"
							onClick={handleRefresh}
							disabled={isRefreshing}
							className="h-8 w-8"
						>
							<RefreshCw
								className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
							/>
						</Button>
						<DialogClose asChild>
							<Button variant="ghost" size="icon" className="h-8 w-8">
								<CloseIcon className="h-4 w-4" />
							</Button>
						</DialogClose>
					</div>
				</DialogHeader>

				<ScrollArea className="flex-1 min-h-0 px-6 max-h-[60vh] overflow-y-auto">
					<div className="space-y-6 pr-4">
						{sortedGroups.map(([messageId, docs]) => (
							<div key={messageId} className="space-y-2">
								{messageId !== "ungrouped" && (
									<div className="text-xs font-medium text-muted-foreground border-b pb-1">
										Message {messageId.substring(0, 8)}...
									</div>
								)}
								<div className="space-y-2">
									{docs.map((doc: any) => (
										<div
											key={doc.id}
											className="grid grid-cols-[auto_1fr_auto] items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors"
										>
											<FileIcon className="flex-shrink-0" />
											<div className="min-w-0 overflow-hidden">
												<Tooltip>
													<TooltipTrigger asChild>
														<p className="text-sm font-medium truncate">
															{doc.filename || doc.title || "Untitled Document"}
														</p>
													</TooltipTrigger>
													<TooltipContent>
														{doc.filename || doc.title || "Untitled Document"}
													</TooltipContent>
												</Tooltip>
												<p className="text-xs text-muted-foreground">
													{formatDate(doc.createdAt)}
												</p>
											</div>
											<Button
												variant="outline"
												size="sm"
												className="text-xs h-8 px-3"
												onClick={() => viewFile(doc.id, doc.url)}
												disabled={isViewLoading}
											>
												View
											</Button>
										</div>
									))}
								</div>
							</div>
						))}

						{(!documents || documents.length === 0) && (
							<div className="text-center py-8">
								<p className="text-sm text-muted-foreground mb-2">
									No files found
								</p>
								<p className="text-xs text-muted-foreground">
									Files uploaded to this chat will appear here
								</p>
							</div>
						)}
					</div>
				</ScrollArea>
			</DialogContent>
		</Dialog>
	);
}
