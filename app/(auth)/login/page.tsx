"use client";

import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import {
  useActionState,
  useEffect,
  useState,
  Suspense,
  useCallback,
  useRef,
} from "react";
import { toast } from "sonner";
import { useTheme } from "next-themes";

import { AuthForm } from "@/components/auth-form";
import { MindMapBackground } from "@/components/mind-map-background";
import { SubmitButton } from "@/components/submit-button";
import { TestimonialCarousel } from "@/components/testimonial-carousel";
import {
  CONTACT,
  PLANS,
  SUBSCRIPTION_STATUS,
  TESTIMONIALS,
} from "@/lib/constants";

import { login, socialLogin, type LoginActionState } from "../actions";
import { LogoIqidis, LogoSignup } from "@/components/icons";
import { identifyUser } from "@/lib/mixpanel/mixpanel";
import { logEvent } from "@/lib/analytics/events-client";
import { AuthEvent, GoogleEvent } from "@/lib/analytics/event-types";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { signIn } from "next-auth/react";

// Create a separate component that uses useSearchParams
function LoginForm() {
  const router = useRouter();
  const params = useSearchParams();
  const isFreemium = params.get("isFreemium");

  const { resolvedTheme } = useTheme();
  
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  // Add a counter to track login attempts
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [showPassword, setShowPassword] = useState(false);

  const [state, formAction] = useActionState<LoginActionState, FormData>(
    login,
    {
      status: "idle",
    } as LoginActionState
  );

  const [showVerificationButton, setShowVerificationButton] = useState(false);
  const [verificationSending, setVerificationSending] = useState(false);

  const sendVerificationEmail = useCallback(async (emailToVerify: string) => {
    try {
      setVerificationSending(true);
      const response = await fetch("/api/auth/email-verification/request", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: emailToVerify }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to send verification email");
      }
      toast.success("Verification instructions have been sent to your email");
    } catch (error) {
      toast.error("Failed to send verification email. Please try again.");
    } finally {
      setVerificationSending(false);
    }
  }, []);

  useEffect(() => {
    if (!state) return;

    if (state.status === "failed") {
      toast.dismiss();
      toast.error("Invalid credentials!");
      setShowVerificationButton(false);
    } else if (state.status === "invalid_data") {
      toast.dismiss();
      toast.error("Please verify your email to login!");
      setShowVerificationButton(true);
    } else if (state.status === "user_not_found") {
      toast.dismiss();
      toast.error("Email is not registered!");
      setShowVerificationButton(false);
    } else if (state.status === "success") {
      toast.dismiss();
      setIsSuccessful(true);
      setIsRedirecting(true);
      if (state?.userId && state?.userEmail) {
        identifyUser(state?.userId, state?.userEmail)
        logEvent(AuthEvent.LOGIN, {
          userId: state.userId,
          email: state.userEmail,
          hours: new Date().getUTCHours(),
        });
      }
      // Add delay before redirecting
      setTimeout(() => {
        // router.refresh();

        // if (isFreemium) {
        //   toast.success(
        //     <div className="flex flex-col p-2">
        //       <p>Welcome to Iqidis! Our free tier includes:</p>
        //       <ul className="list-disc list-outside ps-2">
        //         <li>10 messages per day</li>
        //         <li>10 total saved matter chats</li>
        //         <li>
        //           100 MB of total data saved in your account (chats and uploads)
        //         </li>
        //       </ul>
        //       <p>To unlock more, start your 14-day free trial.</p>
        //     </div>,
        //     {
        //       duration: 5000,
        //     }
        //   );
        // }

        if (state?.isAdmin || state?.subscriptionTier !== PLANS.FREE_PLAN) {
          // Check if there's a returnUrl in the query parameters
          const params = new URLSearchParams(window.location.search);
          const returnUrl = params.get("returnUrl");

          if (returnUrl) {
            router.push(returnUrl);
          } else {
            router.push("/");
          }
        } else {
          router.push("/welcome");
        }
      }, 1500);
    }
    localStorage.setItem("email", state?.userEmail ?? "");
  }, [state, router, isFreemium, loginAttempts]);

  const handleSubmit = (formData: FormData) => {
    const emailValue = formData.get("email") as string;
    const passwordValue = formData.get("password") as string;

    // Reset errors
    setEmailError("");
    setPasswordError("");

    // Validate email
    if (!emailValue) {
      setEmailError("Email is required");
      return;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    // Validate password
    if (!passwordValue) {
      setPasswordError("Password is required");
      return;
    }
    // Remove this client-side validation since we ensure it during signup
    // else if (passwordValue.length < 8) {
    //   toast.dismiss();
    //   toast.error("Password must be at least 8 characters");
    //   return;
    // }

    // If validation passes, proceed with login
    setLoginAttempts((prev) => prev + 1);
    formAction(formData);
  };

  const logoSize = isRedirecting ? 200 : 200;

  return (
    <main className="relative flex md:h-dvh h-full w-screen lg:overflow-hidden overflow-auto">
      {/* Left side - Login form */}
      <section className="w-full md:w-1/2 flex items-center justify-center p-6 bg-[#f9f8fb] dark:bg-slate-800 signBox h-screen">
        <article className="relative w-full max-w-md overflow-hidden flex flex-col gap-6 dark:bg-slate-800 p-8 dark:border-gray-700 dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] animate-fade-in bg-white/70 backdrop-blur-md border border-white/30 shadow-lg rounded-lg">
          <header className="flex flex-col items-center justify-center gap-2 text-center">
            <div className="flex items-center justify-center w-full mb-2">
              <LogoSignup
                size={logoSize}
                mixBlendMode={resolvedTheme === "dark" ? "inherit" : "inherit"}
                isDark={resolvedTheme === "dark"}
              />
            </div>
            <h1 className="text-4xl font-bold font-playfair from-primary to-accent/80 text-transparent bg-clip-text bg-gradient-to-r dark:text-[rgb(var(--base-navy))]">
              Welcome Back
            </h1>
          </header>

          <AuthForm
            action={handleSubmit}
            defaultEmail={email}
            password={password}
            emailError={emailError}
            passwordError={passwordError}
            setEmail={setEmail}
            setPassword={setPassword}
            showPassword={showPassword}
            setShowPassword={setShowPassword}
          >
            <div className="flex flex-col gap-2 mt-2">
              <SubmitButton
                isSuccessful={isSuccessful}
                className="bg-primary text-white font-medium py-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-200 dark:bg-white dark:text-purple-800 dark:border dark:border-purple-300 dark:hover:bg-purple-100"
              >
                Sign in
              </SubmitButton>

              {showVerificationButton && (
                <button
                  type="button"
                  onClick={() => sendVerificationEmail(email)}
                  disabled={verificationSending}
                  className="text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium py-2 border border-indigo-200 dark:border-indigo-800 rounded-md hover:bg-indigo-50 dark:hover:bg-indigo-900/30 transition-colors mt-2"
                >
                  {verificationSending
                    ? "Sending..."
                    : "Resend verification email"}
                </button>
              )}

              <nav className="flex justify-center items-center text-sm mt-1">
                <span className="text-slate-600 dark:text-slate-400">
                  Don&apos;t have an account?
                </span>
                <Link
                  href="/signup"
                  className="text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium ml-1"
                >
                  Sign up
                </Link>
              </nav>
            </div>
          </AuthForm>
          <div className="flex items-center justify-between">
            <hr className="flex-grow border-gray-300 dark:border-gray-700" />
            <span className="mx-2 text-gray-500 dark:text-gray-400">or</span>
            <hr className="flex-grow border-gray-300 dark:border-gray-700" />
          </div>
          {/* <div className="flex justify-center items-center h-10">
            <div 
              className={`transition-all ease-in-out origin-left ${showReferralCode ? 'w-full' : 'w-auto'}`}
              style={{ transitionDuration: '1100ms' }}
            >
              {!showReferralCode ? (
                <button
                  type="button"
                  onClick={() => setShowReferralCode(true)}
                  className="text-indigo-600 hover:text-indigo-800 text-sm font-medium transition-colors duration-200 underline-offset-2 mt-2"
                >
                  Have a referral code?
                </button>
              ) : (
                <div 
                  ref={referralInputRef} 
                  className="animate-in fade-in"
                  style={{ animationDuration: '1100ms' }}
                >
                  <Input
                    id="referralCode"
                    name="referralCode"
                    value={referrerCode}
                    onChange={(e) => setReferrerCode(e.target.value)}
                    onBlur={(e) => fetchReferralCode(e.target.value)}
                    placeholder="Enter referral code"
                    className={`dark:focus:bg-slate-800 dark:bg-slate-800 dark:border-white ${referralCodeError ? "border-red-500" : ""} transition-all`}
                    autoFocus
                  />
                </div>
              )}
            </div>
          </div> */}
          <Button
            onClick={() => {
              logEvent(GoogleEvent.GOOGLE_BUTTON_CLICK);
              signIn("google", { callbackUrl: "/welcome" });
            }}
            variant={"secondary"}
            size={"sm"}
          >
            <svg
              className="mr-2 -ml-1 w-5 h-5"
              viewBox="0 0 533.5 544.3"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill="#4285F4"
                d="M533.5 278.4c0-18.5-1.5-37.1-4.7-55H272.1v104h146.9c-6.3 33.9-25 62.7-53.3 82.2l85.8 66.6c50.2-46.2 82-114.4 82-197.8z"
              />
              <path
                fill="#34A853"
                d="M272.1 544.3c71.7 0 131.8-23.8 175.7-64.9l-85.8-66.6c-23.9 16.1-54.4 25.6-89.9 25.6-69.1 0-127.6-46.6-148.6-109.1H35.2v68.4C78.9 479.9 168.1 544.3 272.1 544.3z"
              />
              <path
                fill="#FBBC05"
                d="M123.5 328.3c-10.5-30.9-10.5-64 0-94.9V165H35.2C12.8 209.4 0 261.2 0 316.1s12.8 106.7 35.2 151.1l88.3-68.9z"
              />
              <path
                fill="#EA4335"
                d="M272.1 107.2c39.2 0 74.3 13.5 101.9 40.2l76.4-76.4C401.8 24.5 343.7 0 272.1 0 168.1 0 78.9 64.4 35.2 165l88.3 68.4c21-62.5 79.5-109.1 148.6-109.1z"
              />
            </svg>
            Continue With Google
          </Button>
        </article>
      </section>

      {/* Right side - Testimonials */}
      <aside className="hidden md:flex md:w-1/2 bg-indigo-50 dark:bg-slate-900/50 flex-col items-center justify-center p-6 overflow-hidden">
        <div className="size-full max-w-lg">
          <TestimonialCarousel fullHeight={true} />
        </div>
      </aside>
    </main>
  );
}

// Main page component with Suspense
export default function Page() {
  return (
    <Suspense
      fallback={
        <div className="flex h-dvh w-screen items-center justify-center bg-background">
          <div className="flex flex-col items-center gap-6">
            <div className="animate-spin-slow">
              <div className="relative flex items-center justify-center">
                <LogoIqidis size={150} />
              </div>
            </div>
          </div>
        </div>
      }
    >
      <LoginForm />
    </Suspense>
  );
}
