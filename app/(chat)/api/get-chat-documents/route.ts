import { auth } from "@/app/(auth)/auth";
import { getChatAttachmentsfromChatId, getMessagesWithAttachmentsByChatId, getSourceDocumentsByChatId } from "@/lib/db/queries";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get("chatId");
  const keyword = searchParams.get("keyword")?.toLowerCase();


  
  if (!chatId) {
    return new Response("Chat ID is required", { status: 400 });
  }

  const session = await auth();
console.log("session:", session?.user);
  if (!session || !session.user) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const documents = await getChatAttachmentsfromChatId({ chatId });

    // If no documents found, return empty array
    if (!documents.length) {
      return Response.json([], { status: 200 });
    }

    // Check if user owns these documents
    if (documents[0].userId !== session.user.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    // If keyword is provided, filter documents
    if (keyword) {
      const filteredDocs = documents.filter(doc => 
        doc.filename.toLowerCase().includes(keyword)
      );
      return Response.json(filteredDocs, { status: 200 });
    }

    return Response.json(documents, { status: 200 });
  } catch (error) {
    console.error("Error fetching source documents:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
